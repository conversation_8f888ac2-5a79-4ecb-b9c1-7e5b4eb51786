using System;
using System.IO;
using System.Text;
using System.Windows.Forms;

namespace ET
{
    /// <summary>
    /// 日志管理类，提供完整的日志记录和管理功能
    /// </summary>
    /// <remarks>
    /// 主要功能包括：
    /// 1. 多级别日志记录（Info、Debug、Warn、Error）
    /// 2. 文件日志和调试输出双重记录
    /// 3. 线程安全的日志写入操作
    /// 4. 异常信息的详细记录
    /// 5. TextBox控件的日志显示支持
    /// 6. 跨线程安全的UI更新
    /// 7. 灵活的日志路径配置（支持子文件夹和完整路径）
    ///
    /// 日志文件特性：
    /// - 按日期自动分割日志文件（yyyy-MM-dd.log）
    /// - UTF-8编码确保中文正确显示
    /// - 自动创建日志目录
    /// - 线程安全的并发写入
    /// - 可配置的日志存储路径
    ///
    /// 使用示例：
    /// <code>
    /// // 基础日志记录
    /// ETLogManager.Info("应用程序启动");
    /// ETLogManager.Error("操作失败", exception);
    ///
    /// // 日志路径配置
    /// ETLogManager.LogPath = "debug";  // 子文件夹模式：[应用程序目录]\logs\debug
    /// ETLogManager.LogPath = @"C:\MyApp\Logs";  // 完整路径模式
    /// ETLogManager.SetLogSubFolder("test");  // 强制子文件夹模式
    /// ETLogManager.ResetLogPathToDefault();  // 重置为默认路径
    ///
    /// // TextBox日志记录（推荐）
    /// ETLogManager.LogToTextBox(textBox, "INFO", "状态更新");
    /// ETLogManager.InfoToTextBox(textBox, "操作完成");
    /// ETLogManager.ErrorToTextBox(textBox, "操作失败", exception);
    /// </code>
    /// </remarks>
    public class ETLogManager
    {
        /// <summary>
        /// 线程同步锁对象，确保日志写入的线程安全
        /// </summary>
        private static readonly object LockObj = new();

        /// <summary>
        /// 是否将日志同时输出到调试窗口
        /// </summary>
        public const bool EnableDebugOutput = true;

        /// <summary>
        /// 是否启用调试级别日志的详细记录和条件检查
        /// </summary>
        /// <remarks>
        /// 当设置为true时：
        /// - Debug级别日志会被记录到文件和控制台
        /// - 调试相关的详细信息会被输出
        /// 当设置为false时：
        /// - Debug级别日志不会被记录
        /// - 只输出Info、Warning、Error级别的日志
        /// </remarks>
        public static bool IsDebugEnabled { get; set; } = true;

        /// <summary>
        /// 启用调试模式
        /// </summary>
        /// <remarks>
        /// 启用后将记录Debug级别的日志到文件和控制台
        /// </remarks>
        public static void EnableDebugMode()
        {
            IsDebugEnabled = true;
            Info("调试模式已启用 - Debug级别日志将被记录");
        }

        /// <summary>
        /// 禁用调试模式
        /// </summary>
        /// <remarks>
        /// 禁用后将不记录Debug级别的日志，只记录Info、Warning、Error级别
        /// </remarks>
        public static void DisableDebugMode()
        {
            Info("调试模式即将禁用 - Debug级别日志将不再记录");
            IsDebugEnabled = false;
        }

        /// <summary>
        /// 日志文件存储路径的私有字段，用于存储实际的日志路径
        /// </summary>
        private static string _logPath = null;

        /// <summary>
        /// 日志文件存储路径，默认为应用程序目录下的logs文件夹
        /// </summary>
        /// <remarks>
        /// 支持两种设置模式：
        /// 1. 文件夹名：如果设置的是相对路径或文件夹名（如"mylogs"），则在默认logs目录下创建该子文件夹
        /// 2. 完整路径：如果设置的是绝对路径（如"C:\MyApp\Logs"），则直接使用该路径作为日志存储目录
        ///
        /// 使用示例：
        /// <code>
        /// // 设置为子文件夹模式
        /// ETLogManager.LogPath = "debug";  // 实际路径：[应用程序目录]\logs\debug
        ///
        /// // 设置为完整路径模式
        /// ETLogManager.LogPath = @"C:\MyApp\Logs";  // 实际路径：C:\MyApp\Logs
        /// </code>
        /// </remarks>
        public static string LogPath
        {
            get
            {
                if (_logPath == null)
                {
                    // 返回默认路径
                    return Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "logs");
                }
                return _logPath;
            }
            set
            {
                if (string.IsNullOrWhiteSpace(value))
                {
                    // 如果设置为空，则重置为默认路径
                    _logPath = null;
                    return;
                }

                // 判断是否为完整路径
                if (Path.IsPathRooted(value))
                {
                    // 完整路径模式：直接使用设置的路径
                    _logPath = value;
                }
                else
                {
                    // 文件夹名模式：在默认logs目录下创建子文件夹
                    string defaultLogsPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "logs");
                    _logPath = Path.Combine(defaultLogsPath, value);
                }
            }
        }

        /// <summary>
        /// 重置日志路径为默认值（应用程序目录下的logs文件夹）
        /// </summary>
        public static void ResetLogPathToDefault()
        {
            _logPath = null;
        }

        /// <summary>
        /// 获取当前实际使用的日志路径（用于调试和诊断）
        /// </summary>
        /// <returns>当前日志存储的完整路径</returns>
        public static string GetCurrentLogPath()
        {
            return LogPath;
        }

        /// <summary>
        /// 设置日志路径为指定的子文件夹（在默认logs目录下）
        /// </summary>
        /// <param name="subFolderName">子文件夹名称</param>
        /// <remarks>
        /// 此方法强制在默认logs目录下创建子文件夹，即使传入完整路径也会被当作文件夹名处理
        /// </remarks>
        public static void SetLogSubFolder(string subFolderName)
        {
            if (string.IsNullOrWhiteSpace(subFolderName))
            {
                ResetLogPathToDefault();
                return;
            }

            // 强制在默认logs目录下创建子文件夹
            string defaultLogsPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "logs");
            _logPath = Path.Combine(defaultLogsPath, subFolderName);
        }

        /// <summary>
        /// 写入信息日志
        /// </summary>
        public static void Info(string message)
        {
            WriteLog("INFO", message);
        }

        /// <summary>
        /// 写入信息日志（带源标识）
        /// </summary>
        public static void Info(object source, string message)
        {
            WriteLog("INFO", source, message);
        }

        /// <summary>
        /// 写入信息日志（带异常信息）
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="ex">异常对象</param>
        public static void Info(string message, Exception ex)
        {
            WriteLog("INFO", $"{message}\r\n异常详情：{ex}");
        }

        /// <summary>
        /// 写入信息日志（带源标识、消息和异常信息）
        /// </summary>
        /// <param name="source">日志源标识</param>
        /// <param name="message">日志消息</param>
        /// <param name="ex">异常对象</param>
        public static void Info(object source, string message, Exception ex)
        {
            WriteLog("INFO", source, $"{message}\r\n异常详情：{ex}");
        }

        /// <summary>
        /// 写入信息日志（带源标识和异常信息）
        /// </summary>
        /// <param name="source">日志源标识</param>
        /// <param name="ex">异常对象</param>
        public static void Info(object source, Exception ex)
        {
            WriteLog("INFO", source, ex.ToString());
        }

        /// <summary>
        /// 写入信息日志（仅异常信息）
        /// </summary>
        /// <param name="ex">异常对象</param>
        public static void Info(Exception ex)
        {
            string message = $"异常信息：{ex.Message}";

            if (ex.InnerException != null)
            {
                message += $"\r\n内部异常：{ex.InnerException.Message}";
            }

            message += $"\r\n堆栈跟踪：{ex.StackTrace}";

            WriteLog("INFO", message);
        }

        /// <summary>
        /// 写入ETException信息日志
        /// </summary>
        /// <param name="ex">ETException异常</param>
        public static void Info(ETException ex)
        {
            string operation = !string.IsNullOrEmpty(ex.Operation) ? ex.Operation : "未知操作";
            string message = $"{ex.Message}";

            if (ex.InnerException != null)
            {
                message += $"\r\n内部异常：{ex.InnerException.Message}";
            }

            WriteLog("INFO", operation, message);
        }

        /// <summary>
        /// 写入调试日志
        /// </summary>
        /// <remarks>
        /// 只有在 IsDebugEnabled 为 true 时才会记录调试日志
        /// </remarks>
        public static void Debug(string message)
        {
            if (!IsDebugEnabled) return;
            WriteLog("DEBUG", message);
        }

        /// <summary>
        /// 写入调试日志（带源标识）
        /// </summary>
        /// <remarks>
        /// 只有在 IsDebugEnabled 为 true 时才会记录调试日志
        /// </remarks>
        public static void Debug(object source, string message)
        {
            if (!IsDebugEnabled) return;
            WriteLog("DEBUG", source, message);
        }

        /// <summary>
        /// 写入调试日志（带异常信息）
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="ex">异常对象</param>
        /// <remarks>
        /// 只有在 IsDebugEnabled 为 true 时才会记录调试日志
        /// </remarks>
        public static void Debug(string message, Exception ex)
        {
            if (!IsDebugEnabled) return;
            WriteLog("DEBUG", $"{message}\r\n异常详情：{ex}");
        }

        /// <summary>
        /// 写入调试日志（带源标识、消息和异常信息）
        /// </summary>
        /// <param name="source">日志源标识</param>
        /// <param name="message">日志消息</param>
        /// <param name="ex">异常对象</param>
        public static void Debug(object source, string message, Exception ex)
        {
            WriteLog("DEBUG", source, $"{message}\r\n异常详情：{ex}");
        }

        /// <summary>
        /// 写入调试日志（带源标识和异常信息）
        /// </summary>
        /// <param name="source">日志源标识</param>
        /// <param name="ex">异常对象</param>
        public static void Debug(object source, Exception ex)
        {
            WriteLog("DEBUG", source, ex.ToString());
        }

        /// <summary>
        /// 写入调试日志（仅异常信息）
        /// </summary>
        /// <param name="ex">异常对象</param>
        /// <remarks>
        /// 只有在 IsDebugEnabled 为 true 时才会记录调试日志
        /// </remarks>
        public static void Debug(Exception ex)
        {
            if (!IsDebugEnabled) return;

            string message = $"异常信息：{ex.Message}";

            if (ex.InnerException != null)
            {
                message += $"\r\n内部异常：{ex.InnerException.Message}";
            }

            message += $"\r\n堆栈跟踪：{ex.StackTrace}";

            WriteLog("DEBUG", message);
        }

        /// <summary>
        /// 写入ETException调试日志
        /// </summary>
        /// <param name="ex">ETException异常</param>
        /// <remarks>
        /// 只有在 IsDebugEnabled 为 true 时才会记录调试日志
        /// </remarks>
        public static void Debug(ETException ex)
        {
            if (!IsDebugEnabled) return;

            string operation = !string.IsNullOrEmpty(ex.Operation) ? ex.Operation : "未知操作";
            string message = $"{ex.Message}";

            if (ex.InnerException != null)
            {
                message += $"\r\n内部异常：{ex.InnerException.Message}";
            }

            WriteLog("DEBUG", operation, message);
        }

        /// <summary>
        /// 写入警告日志
        /// </summary>
        public static void Warning(string message)
        {
            WriteLog("WARN", message);
        }

        /// <summary>
        /// 写入警告日志（带源标识）
        /// </summary>
        public static void Warning(object source, string message)
        {
            WriteLog("WARN", source, message);
        }

        /// <summary>
        /// 写入警告日志（带异常信息）
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="ex">异常对象</param>
        public static void Warning(string message, Exception ex)
        {
            WriteLog("WARN", $"{message}\r\n异常详情：{ex}");
        }

        /// <summary>
        /// 写入警告日志（带源标识、消息和异常信息）
        /// </summary>
        /// <param name="source">日志源标识</param>
        /// <param name="message">日志消息</param>
        /// <param name="ex">异常对象</param>
        public static void Warning(object source, string message, Exception ex)
        {
            WriteLog("WARN", source, $"{message}\r\n异常详情：{ex}");
        }

        /// <summary>
        /// 写入警告日志（带源标识和异常信息）
        /// </summary>
        /// <param name="source">日志源标识</param>
        /// <param name="ex">异常对象</param>
        public static void Warning(object source, Exception ex)
        {
            WriteLog("WARN", source, ex.ToString());
        }

        /// <summary>
        /// 写入警告日志（仅异常信息）
        /// </summary>
        /// <param name="ex">异常对象</param>
        public static void Warning(Exception ex)
        {
            string message = $"异常信息：{ex.Message}";

            if (ex.InnerException != null)
            {
                message += $"\r\n内部异常：{ex.InnerException.Message}";
            }

            message += $"\r\n堆栈跟踪：{ex.StackTrace}";

            WriteLog("WARN", message);
        }

        /// <summary>
        /// 写入ETException警告日志
        /// </summary>
        /// <param name="ex">ETException异常</param>
        public static void Warning(ETException ex)
        {
            string operation = !string.IsNullOrEmpty(ex.Operation) ? ex.Operation : "未知操作";
            string message = $"{ex.Message}";

            if (ex.InnerException != null)
            {
                message += $"\r\n内部异常：{ex.InnerException.Message}";
            }

            WriteLog("WARN", operation, message);
        }

        #region Warn方法（过期版本，建议使用Warning方法）

        /// <summary>
        /// 写入警告日志
        /// </summary>
        /// <param name="message">日志消息</param>
        [Obsolete("此方法已过期，请使用 Warning(string message) 方法替代", false)]
        public static void Warn(string message)
        {
            Warning(message);
        }

        /// <summary>
        /// 写入警告日志（带源标识）
        /// </summary>
        /// <param name="source">日志源标识</param>
        /// <param name="message">日志消息</param>
        [Obsolete("此方法已过期，请使用 Warning(object source, string message) 方法替代", false)]
        public static void Warn(object source, string message)
        {
            Warning(source, message);
        }

        /// <summary>
        /// 写入警告日志（带异常信息）
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="ex">异常对象</param>
        [Obsolete("此方法已过期，请使用 Warning(string message, Exception ex) 方法替代", false)]
        public static void Warn(string message, Exception ex)
        {
            Warning(message, ex);
        }

        /// <summary>
        /// 写入警告日志（带源标识和异常信息）
        /// </summary>
        /// <param name="source">日志源标识</param>
        /// <param name="ex">异常对象</param>
        [Obsolete("此方法已过期，请使用 Warning(object source, Exception ex) 方法替代", false)]
        public static void Warn(object source, Exception ex)
        {
            Warning(source, ex);
        }

        /// <summary>
        /// 写入警告日志（仅异常信息）
        /// </summary>
        /// <param name="ex">异常对象</param>
        [Obsolete("此方法已过期，请使用 Warning(Exception ex) 方法替代", false)]
        public static void Warn(Exception ex)
        {
            Warning(ex);
        }

        /// <summary>
        /// 写入ETException警告日志
        /// </summary>
        /// <param name="ex">ETException异常</param>
        [Obsolete("此方法已过期，请使用 Warning(ETException ex) 方法替代", false)]
        public static void Warn(ETException ex)
        {
            Warning(ex);
        }

        #endregion

        /// <summary>
        /// 写入错误日志
        /// </summary>
        public static void Error(string message)
        {
            WriteLog("ERROR", message);
        }

        /// <summary>
        /// 写入错误日志（带源标识）
        /// </summary>
        public static void Error(object source, string message)
        {
            WriteLog("ERROR", source, message);
        }

        /// <summary>
        /// 写入错误日志（带异常信息）
        /// </summary>
        public static void Error(string message, Exception ex)
        {
            WriteLog("ERROR", $"{message}\r\n异常详情：{ex}");
        }

        /// <summary>
        /// 写入错误日志（带源标识、消息和异常信息）
        /// </summary>
        /// <param name="source">日志源标识</param>
        /// <param name="message">日志消息</param>
        /// <param name="ex">异常对象</param>
        public static void Error(object source, string message, Exception ex)
        {
            WriteLog("ERROR", source, $"{message}\r\n异常详情：{ex}");
        }

        /// <summary>
        /// 写入错误日志（带源标识和异常信息）
        /// </summary>
        public static void Error(object source, Exception ex)
        {
            WriteLog("ERROR", source, ex.ToString());
        }



        /// <summary>
        /// 写入错误日志（仅异常信息）
        /// </summary>
        /// <param name="ex">异常对象</param>
        public static void Error(Exception ex)
        {
            string message = $"异常信息：{ex.Message}";

            if (ex.InnerException != null)
            {
                message += $"\r\n内部异常：{ex.InnerException.Message}";
            }

            message += $"\r\n堆栈跟踪：{ex.StackTrace}";

            WriteLog("ERROR", message);
        }

        /// <summary>
        /// 写入ETException错误日志
        /// </summary>
        /// <param name="ex">ETException异常</param>
        public static void Error(ETException ex)
        {
            string operation = !string.IsNullOrEmpty(ex.Operation) ? ex.Operation : "未知操作";
            string message = $"{ex.Message}";

            if (ex.InnerException != null)
            {
                message += $"\r\n内部异常：{ex.InnerException.Message}";
            }

            WriteLog("ERROR", operation, message);
        }

        /// <summary>
        /// 写入日志的核心方法（无源标识）
        /// </summary>
        /// <param name="level">日志级别</param>
        /// <param name="message">日志消息</param>
        private static void WriteLog(string level, string message)
        {
            WriteLog(level, null, message);
        }

        /// <summary>
        /// 写入日志的核心方法（带源标识）
        /// </summary>
        /// <param name="level">日志级别</param>
        /// <param name="source">日志源标识</param>
        /// <param name="message">日志消息</param>
        /// <remarks>
        /// 此方法是所有日志写入的核心实现，具有以下特性：
        /// 1. 线程安全：使用锁确保并发写入安全
        /// 2. 自动目录创建：确保日志目录存在
        /// 3. 双重输出：同时写入文件和调试窗口
        /// 4. 异常安全：写入失败不会影响程序运行
        /// </remarks>
        private static void WriteLog(string level, object source, string message)
        {
            lock (LockObj)
            {
                try
                {
                    // 生成日志文件名（按日期分割）
                    string fileName = Path.Combine(LogPath, $"{DateTime.Now:yyyy-MM-dd}.log");
                    string sourceInfo = source != null ? $"[{source}] " : string.Empty;
                    string logMessage = $"{DateTime.Now:yyyy-MM-dd HH:mm:ss} [{level}] {sourceInfo}{message}";

                    // 确保日志目录存在
                    if (!Directory.Exists(LogPath))
                    {
                        Directory.CreateDirectory(LogPath);
                    }

                    // 写入文件（使用UTF-8编码）
                    File.AppendAllText(fileName, $"{logMessage}{Environment.NewLine}", Encoding.UTF8);

                    // 如果启用了调试输出，则同时输出到调试窗口
                    if (EnableDebugOutput)
                    {
                        System.Diagnostics.Debug.Print(logMessage);
                    }
                }
                catch
                {
                    // 写入日志失败时不抛出异常，避免影响主程序运行
                }
            }
        }

        /// <summary>
        /// 将原始消息直接写入到TextBox控件中（底层方法）
        /// </summary>
        /// <param name="textBox">目标TextBox控件</param>
        /// <param name="message">原始消息内容</param>
        /// <remarks>
        /// 这是一个底层方法，直接将消息写入TextBox而不进行任何格式化。
        /// 通常应使用LogToTextBox方法来获得完整的日志功能。
        ///
        /// 特性：
        /// - 线程安全的跨线程调用处理
        /// - 自动滚动到底部
        /// - 异常安全，不会因UI错误影响程序运行
        /// </remarks>
        private static void WriteRawMessageToTextBox(TextBox textBox, string message)
        {
            if (textBox == null) return;

            try
            {
                // 需要检查是否需要跨线程调用
                if (textBox.InvokeRequired)
                {
                    // 使用Invoke而不是BeginInvoke避免内存泄漏
                    textBox.Invoke(new Action<TextBox, string>(WriteRawMessageToTextBox), textBox, message);
                    return;
                }

                // 添加消息到TextBox
                textBox.AppendText($"{message}{Environment.NewLine}");

                // 滚动到底部
                textBox.SelectionStart = textBox.Text.Length;
                textBox.ScrollToCaret();
            }
            catch
            {
                // 忽略在写入TextBox时可能发生的错误
            }
        }

        /// <summary>
        /// 将原始消息直接写入到TextBox控件中
        /// </summary>
        /// <param name="textBox">目标TextBox控件</param>
        /// <param name="message">原始消息内容</param>
        /// <remarks>
        /// 兼容性方法，建议使用LogToTextBox获得完整的日志功能
        /// </remarks>
        [Obsolete("建议使用LogToTextBox方法获得完整的日志功能，此方法将在未来版本中移除")]
        public static void WriteToTextBox(TextBox textBox, string message)
        {
            WriteRawMessageToTextBox(textBox, message);
        }

        /// <summary>
        /// 格式化并写入日志消息到TextBox控件
        /// </summary>
        /// <param name="textBox">目标TextBox控件</param>
        /// <param name="level">日志级别</param>
        /// <param name="message">日志消息</param>
        /// <param name="writeToFile">是否同时写入到日志文件</param>
        /// <remarks>
        /// 这是推荐的TextBox日志记录方法，提供完整的日志功能：
        /// - 自动添加时间戳和级别标识
        /// - 可选择是否同时写入日志文件
        /// - 线程安全的UI更新
        /// </remarks>
        public static void LogToTextBox(TextBox textBox, string level, string message, bool writeToFile = true)
        {
            string logMessage = $"{DateTime.Now:yyyy-MM-dd HH:mm:ss} {level}：{message}";

            // 写入到TextBox
            WriteRawMessageToTextBox(textBox, logMessage);

            // 同时写入到日志文件
            if (writeToFile)
            {
                WriteLog(level, message);
            }
        }

        /// <summary>
        /// 写入信息级别日志到TextBox控件
        /// </summary>
        /// <param name="textBox">目标TextBox控件</param>
        /// <param name="message">日志消息</param>
        /// <param name="writeToFile">是否同时写入到日志文件</param>
        public static void InfoToTextBox(TextBox textBox, string message, bool writeToFile = true)
        {
            LogToTextBox(textBox, "信息", message, writeToFile);
        }

        /// <summary>
        /// 写入调试级别日志到TextBox控件
        /// </summary>
        /// <param name="textBox">目标TextBox控件</param>
        /// <param name="message">日志消息</param>
        /// <param name="writeToFile">是否同时写入到日志文件</param>
        /// <remarks>
        /// 只有在 IsDebugEnabled 为 true 时才会记录调试日志
        /// </remarks>
        public static void DebugToTextBox(TextBox textBox, string message, bool writeToFile = true)
        {
            if (!IsDebugEnabled) return;
            LogToTextBox(textBox, "调试", message, writeToFile);
        }

        /// <summary>
        /// 写入警告级别日志到TextBox控件
        /// </summary>
        /// <param name="textBox">目标TextBox控件</param>
        /// <param name="message">日志消息</param>
        /// <param name="writeToFile">是否同时写入到日志文件</param>
        public static void WarningToTextBox(TextBox textBox, string message, bool writeToFile = true)
        {
            LogToTextBox(textBox, "警告", message, writeToFile);
        }

        /// <summary>
        /// 写入错误级别日志到TextBox控件
        /// </summary>
        /// <param name="textBox">目标TextBox控件</param>
        /// <param name="message">日志消息</param>
        /// <param name="writeToFile">是否同时写入到日志文件</param>
        public static void ErrorToTextBox(TextBox textBox, string message, bool writeToFile = true)
        {
            LogToTextBox(textBox, "错误", message, writeToFile);
        }

        /// <summary>
        /// 写入错误级别日志到TextBox控件（带异常信息）
        /// </summary>
        /// <param name="textBox">目标TextBox控件</param>
        /// <param name="message">日志消息</param>
        /// <param name="ex">异常对象</param>
        /// <param name="writeToFile">是否同时写入到日志文件</param>
        public static void ErrorToTextBox(TextBox textBox, string message, Exception ex, bool writeToFile = true)
        {
            string fullMessage = $"{message}\r\n异常详情：{ex}";
            LogToTextBox(textBox, "错误", fullMessage, writeToFile);
        }

        /// <summary>
        /// 格式化并写入日志消息到多个TextBox控件
        /// </summary>
        /// <param name="primaryTextBox">主要TextBox控件</param>
        /// <param name="secondaryTextBox">次要TextBox控件（可选）</param>
        /// <param name="level">日志级别</param>
        /// <param name="message">日志消息</param>
        /// <param name="writeToSecondary">是否写入到次要TextBox</param>
        /// <param name="writeToFile">是否同时写入到日志文件</param>
        /// <remarks>
        /// 用于需要同时向多个TextBox输出日志的场景，如主界面和详细日志窗口
        /// </remarks>
        public static void LogToMultipleTextBoxes(TextBox primaryTextBox, TextBox secondaryTextBox, string level, string message, bool writeToSecondary = false, bool writeToFile = true)
        {
            string logMessage = $"{DateTime.Now:yyyy-MM-dd HH:mm:ss} {level}：{message}";

            // 写入到主TextBox
            WriteRawMessageToTextBox(primaryTextBox, logMessage);

            // 写入到次要TextBox（如果需要）
            if (writeToSecondary && secondaryTextBox != null)
            {
                WriteRawMessageToTextBox(secondaryTextBox, logMessage);
            }

            // 同时写入到日志文件
            if (writeToFile)
            {
                WriteLog(level, message);
            }
        }
    }
}