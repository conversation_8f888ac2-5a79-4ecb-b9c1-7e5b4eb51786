using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Linq;
using System.Text;
using System.IO;
using CefSharp;
using CefSharp.WinForms;
using Newtonsoft.Json;
using ET.ETOAAutomation.Models;
using ET.ETIniFile;
using ET;
using ET.ETException;

namespace ET.ETOAAutomation
{
    /// <summary>
    /// 基于CefSharp的OA系统登录浏览器，专门处理登录认证 窗体文件：包含ETOALoginBrowser.cs、ETOALoginBrowser.Designer.cs、ETOALoginBrowser.resx
    /// </summary>
    public partial class ETOALoginBrowser : Form
    {
        #region 私有字段

        private ChromiumWebBrowser _browser;
        private ETOALoginInfo _loginInfo;
        private bool _isLoginSuccessful = false;
        private Dictionary<string, string> _cookies = new Dictionary<string, string>();
        private TaskCompletionSource<ETOALoginInfo> _loginTaskCompletionSource;
        private string _username;
        private string _password;
        private bool _autoLoginMode = false;
        private Timer _loginCheckTimer;
        private int _loginCheckAttempts = 0;
        private const int MAX_LOGIN_CHECK_ATTEMPTS = 30; // 最多检查30次（15秒）

        #endregion 私有字段

        #region 公共属性

        /// <summary>
        /// 登录URL
        /// </summary>
        public string LoginUrl { get; set; }

        /// <summary>
        /// 登录是否成功
        /// </summary>
        public bool IsLoginSuccessful => _isLoginSuccessful;

        /// <summary>
        /// Cookie信息
        /// </summary>
        public Dictionary<string, string> Cookies => _cookies;

        /// <summary>
        /// CefSharp浏览器控件
        /// </summary>
        public ChromiumWebBrowser Browser => _browser;

        /// <summary>
        /// 关闭按钮
        /// </summary>
        public Button BtnClose { get; private set; }

        /// <summary>
        /// 状态标签
        /// </summary>
        public Label LblStatus { get; private set; }

        /// <summary>
        /// 进度条
        /// </summary>
        public ProgressBar ProgressBar { get; private set; }

        /// <summary>
        /// 刷新按钮
        /// </summary>
        public Button BtnRefresh { get; private set; }

        /// <summary>
        /// 标题标签
        /// </summary>
        public Label LblTitle { get; private set; }

        #endregion 公共属性

        #region 构造函数

        /// <summary>
        /// 初始化登录浏览器
        /// </summary>
        /// <param name="loginUrl">登录URL</param>
        public ETOALoginBrowser(string loginUrl)
        {
            try
            {
                LoginUrl = loginUrl ?? throw new ArgumentNullException(nameof(loginUrl));

                ETLogManager.Info($"初始化ETOALoginBrowser，登录URL: {loginUrl}");

                InitializeComponent();
                InitializeBrowser();
                InitializeTimer();

                ETLogManager.Info("ETOALoginBrowser初始化完成");
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"ETOALoginBrowser初始化失败: {ex.Message}", ex);
                throw new ETOAException("登录浏览器初始化失败", ex);
            }
        }

        #endregion 构造函数

        #region 私有方法

        /// <summary>
        /// 初始化浏览器
        /// </summary>
        private void InitializeBrowser()
        {
            try
            {
                _browser = new ChromiumWebBrowser(LoginUrl)
                {
                    Dock = DockStyle.Fill
                };

                // 添加浏览器到窗体
                this.Controls.Add(_browser);

                // 绑定事件
                _browser.LoadingStateChanged += Browser_LoadingStateChanged;
                _browser.AddressChanged += Browser_AddressChanged;
                _browser.LoadEnd += Browser_LoadEnd;
                _browser.JavascriptException += Browser_JavascriptException;

                ETLogManager.Info("浏览器控件初始化完成");
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"浏览器控件初始化失败: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// 初始化定时器
        /// </summary>
        private void InitializeTimer()
        {
            _loginCheckTimer = new Timer();
            _loginCheckTimer.Interval = 500; // 每500毫秒检查一次
            _loginCheckTimer.Tick += LoginCheckTimer_Tick;
        }

        /// <summary>
        /// 浏览器加载状态改变事件
        /// </summary>
        private void Browser_LoadingStateChanged(object sender, LoadingStateChangedEventArgs e)
        {
            try
            {
                if (!e.IsLoading)
                {
                    ETLogManager.Info($"页面加载完成: {_browser.Address}");

                    // 如果是自动登录模式，尝试自动填写表单
                    if (_autoLoginMode && !string.IsNullOrEmpty(_username) && !string.IsNullOrEmpty(_password))
                    {
                        _ = Task.Run(async () => await TryAutoFillLoginForm());
                    }

                    // 检查登录状态
                    _ = Task.Run(async () => await CheckLoginStatusAsync());
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"浏览器加载状态改变事件处理失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 浏览器地址改变事件
        /// </summary>
        private void Browser_AddressChanged(object sender, AddressChangedEventArgs e)
        {
            try
            {
                ETLogManager.Info($"浏览器地址改变: {e.Address}");

                // 更新状态显示
                if (LblStatus != null)
                {
                    this.Invoke(new Action(() =>
                    {
                        LblStatus.Text = $"当前页面: {e.Address}";
                    }));
                }

                // 地址改变时检查登录状态
                _ = Task.Run(async () => await CheckLoginStatusAsync());
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"浏览器地址改变事件处理失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 浏览器加载结束事件
        /// </summary>
        private void Browser_LoadEnd(object sender, LoadEndEventArgs e)
        {
            try
            {
                if (e.Frame.IsMain)
                {
                    ETLogManager.Info($"主框架加载完成: {e.Frame.Url}");
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"浏览器加载结束事件处理失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// JavaScript异常事件
        /// </summary>
        private void Browser_JavascriptException(object sender, JavascriptExceptionEventArgs e)
        {
            ETLogManager.Warn($"JavaScript异常: {e.Message} 在 {e.SourceLine}:{e.LineNumber}");
        }

        /// <summary>
        /// 定时器检查登录状态
        /// </summary>
        private async void LoginCheckTimer_Tick(object sender, EventArgs e)
        {
            try
            {
                _loginCheckAttempts++;

                if (_loginCheckAttempts > MAX_LOGIN_CHECK_ATTEMPTS)
                {
                    _loginCheckTimer.Stop();
                    ETLogManager.Warn("登录检查超时，停止自动检查");
                    return;
                }

                await CheckLoginStatusAsync();
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"定时器检查登录状态失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 检查登录状态（异步版本）
        /// </summary>
        private async Task CheckLoginStatusAsync()
        {
            try
            {
                if (_browser?.IsBrowserInitialized != true)
                    return;

                // 获取当前URL
                var currentUrl = _browser.Address;
                ETLogManager.Info($"检查登录状态，当前URL: {currentUrl}");

                // 检查是否登录成功（根据URL变化判断）
                if (IsLoginSuccessUrl(currentUrl))
                {
                    _isLoginSuccessful = true;
                    await ExtractLoginInfoAsync();

                    // 停止定时器检查
                    _loginCheckTimer?.Stop();

                    // 如果有等待的任务，完成它
                    if (_loginTaskCompletionSource != null && !_loginTaskCompletionSource.Task.IsCompleted)
                    {
                        _loginTaskCompletionSource.SetResult(_loginInfo);
                    }

                    ETLogManager.Info("登录成功检测完成");
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"检查登录状态失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 判断URL是否表示登录成功
        /// </summary>
        private bool IsLoginSuccessUrl(string url)
        {
            if (string.IsNullOrEmpty(url))
                return false;

            // 常见的登录成功URL特征
            var successIndicators = new[] { "dashboard", "main", "home", "index", "portal", "workspace" };
            var failureIndicators = new[] { "login", "signin", "error", "fail", "denied" };

            // 如果包含失败指示器，则未登录成功
            if (failureIndicators.Any(indicator => url.ToLower().Contains(indicator)))
                return false;

            // 如果包含成功指示器，则可能登录成功
            return successIndicators.Any(indicator => url.ToLower().Contains(indicator));
        }

        /// <summary>
        /// 提取登录信息（异步版本）
        /// </summary>
        private async Task ExtractLoginInfoAsync()
        {
            try
            {
                ETLogManager.Info("开始提取登录信息");

                // 获取Cookie信息
                var cookies = await GetAllCookiesAsync();

                // 获取用户信息（通过JavaScript）
                var userInfo = await GetUserInfoFromPageAsync();

                _loginInfo = new ETOALoginInfo
                {
                    IsSuccess = _isLoginSuccessful,
                    LoginTime = DateTime.Now,
                    Cookies = cookies,
                    BaseUrl = GetBaseUrl(LoginUrl),
                    LoginUrl = LoginUrl,
                    RedirectUrl = _browser.Address,
                    Username = userInfo.ContainsKey("username") ? userInfo["username"] : _username,
                    UserId = userInfo.ContainsKey("userId") ? userInfo["userId"] : "",
                    DisplayName = userInfo.ContainsKey("displayName") ? userInfo["displayName"] : "",
                    Token = userInfo.ContainsKey("token") ? userInfo["token"] : "",
                    SessionId = userInfo.ContainsKey("sessionId") ? userInfo["sessionId"] : ""
                };

                // 提取请求头信息
                await ExtractRequestHeadersAsync();

                ETLogManager.Info($"登录信息提取完成，用户: {_loginInfo.Username}");
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"提取登录信息失败: {ex.Message}", ex);
                throw new ETOAException("提取登录信息失败", ex);
            }
        }

        /// <summary>
        /// 获取所有Cookie
        /// </summary>
        private async Task<Dictionary<string, string>> GetAllCookiesAsync()
        {
            var cookies = new Dictionary<string, string>();

            try
            {
                var cookieManager = _browser.GetCookieManager();
                var visitor = new CookieVisitor();

                if (cookieManager.VisitAllCookies(visitor))
                {
                    await visitor.Task;

                    foreach (var cookie in visitor.Cookies)
                    {
                        if (!cookies.ContainsKey(cookie.Name))
                        {
                            cookies.Add(cookie.Name, cookie.Value);
                        }
                    }
                }

                ETLogManager.Info($"获取到 {cookies.Count} 个Cookie");
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"获取Cookie失败: {ex.Message}", ex);
            }

            return cookies;
        }

        /// <summary>
        /// 从页面获取用户信息
        /// </summary>
        private async Task<Dictionary<string, string>> GetUserInfoFromPageAsync()
        {
            var userInfo = new Dictionary<string, string>();

            try
            {
                // 尝试从常见的JavaScript变量中获取用户信息
                var scripts = new[]
                {
                    "window.user ? JSON.stringify(window.user) : null",
                    "window.userInfo ? JSON.stringify(window.userInfo) : null",
                    "window.currentUser ? JSON.stringify(window.currentUser) : null",
                    "localStorage.getItem('user') || localStorage.getItem('userInfo')",
                    "sessionStorage.getItem('user') || sessionStorage.getItem('userInfo')"
                };

                foreach (var script in scripts)
                {
                    var result = await _browser.EvaluateScriptAsync(script);
                    if (result.Success && result.Result != null)
                    {
                        var jsonStr = result.Result.ToString();
                        if (!string.IsNullOrEmpty(jsonStr) && jsonStr != "null")
                        {
                            try
                            {
                                var userData = JsonConvert.DeserializeObject<Dictionary<string, object>>(jsonStr);
                                foreach (var kvp in userData)
                                {
                                    userInfo[kvp.Key] = kvp.Value?.ToString() ?? "";
                                }
                                break;
                            }
                            catch
                            {
                                // 忽略JSON解析错误，继续尝试下一个脚本
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Warn($"从页面获取用户信息失败: {ex.Message}");
            }

            return userInfo;
        }

        /// <summary>
        /// 提取请求头信息
        /// </summary>
        private async Task ExtractRequestHeadersAsync()
        {
            try
            {
                // 这里可以通过监控网络请求来获取请求头 暂时使用基础的请求头设置
                if (_loginInfo.Headers == null)
                    _loginInfo.Headers = new Dictionary<string, string>();

                _loginInfo.Headers["User-Agent"] = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36";
                _loginInfo.Headers["Accept"] = "application/json, text/plain, */*";
                _loginInfo.Headers["Accept-Language"] = "zh-CN,zh;q=0.9,en;q=0.8";

                ETLogManager.Info("请求头信息提取完成");
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"提取请求头信息失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 获取基础URL
        /// </summary>
        private string GetBaseUrl(string url)
        {
            try
            {
                var uri = new Uri(url);
                return $"{uri.Scheme}://{uri.Host}:{uri.Port}";
            }
            catch
            {
                return url;
            }
        }

        /// <summary>
        /// 尝试自动填写登录表单
        /// </summary>
        private async Task TryAutoFillLoginForm()
        {
            try
            {
                ETLogManager.Info("尝试自动填写登录表单");

                // 等待页面完全加载
                await Task.Delay(1000);

                // 常见的用户名和密码字段选择器
                var usernameSelectors = new[]
                {
                    "input[name='username']", "input[name='user']", "input[name='account']",
                    "input[id='username']", "input[id='user']", "input[id='account']",
                    "input[type='text']:first", "#username", "#user", "#account"
                };

                var passwordSelectors = new[]
                {
                    "input[name='password']", "input[name='pwd']", "input[name='pass']",
                    "input[id='password']", "input[id='pwd']", "input[id='pass']",
                    "input[type='password']", "#password", "#pwd", "#pass"
                };

                // 尝试填写用户名
                foreach (var selector in usernameSelectors)
                {
                    var script = $@"
                        var element = document.querySelector('{selector}');
                        if (element) {{
                            element.value = '{_username}';
                            element.dispatchEvent(new Event('input', {{ bubbles: true }}));
                            element.dispatchEvent(new Event('change', {{ bubbles: true }}));
                            true;
                        }} else {{
                            false;
                        }}
                    ";

                    var result = await _browser.EvaluateScriptAsync(script);
                    if (result.Success && (bool)result.Result)
                    {
                        ETLogManager.Info($"成功填写用户名，选择器: {selector}");
                        break;
                    }
                }

                // 尝试填写密码
                foreach (var selector in passwordSelectors)
                {
                    var script = $@"
                        var element = document.querySelector('{selector}');
                        if (element) {{
                            element.value = '{_password}';
                            element.dispatchEvent(new Event('input', {{ bubbles: true }}));
                            element.dispatchEvent(new Event('change', {{ bubbles: true }}));
                            true;
                        }} else {{
                            false;
                        }}
                    ";

                    var result = await _browser.EvaluateScriptAsync(script);
                    if (result.Success && (bool)result.Result)
                    {
                        ETLogManager.Info($"成功填写密码，选择器: {selector}");
                        break;
                    }
                }

                // 等待一下再尝试提交
                await Task.Delay(500);

                // 尝试点击登录按钮
                await TryClickLoginButton();
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"自动填写登录表单失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 尝试点击登录按钮
        /// </summary>
        private async Task TryClickLoginButton()
        {
            try
            {
                var buttonSelectors = new[]
                {
                    "button[type='submit']", "input[type='submit']",
                    "button:contains('登录')", "button:contains('Login')", "button:contains('登陆')",
                    "#loginBtn", "#login", ".login-btn", ".btn-login"
                };

                foreach (var selector in buttonSelectors)
                {
                    var script = $@"
                        var element = document.querySelector('{selector}');
                        if (element) {{
                            element.click();
                            true;
                        }} else {{
                            false;
                        }}
                    ";

                    var result = await _browser.EvaluateScriptAsync(script);
                    if (result.Success && (bool)result.Result)
                    {
                        ETLogManager.Info($"成功点击登录按钮，选择器: {selector}");

                        // 开始定时检查登录状态
                        _loginCheckTimer.Start();
                        return;
                    }
                }

                ETLogManager.Warn("未找到登录按钮");
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"点击登录按钮失败: {ex.Message}", ex);
            }
        }

        #endregion 私有方法

        #region 公共方法

        /// <summary>
        /// 显示登录对话框并获取认证信息
        /// </summary>
        /// <returns>登录信息</returns>
        public async Task<ETOALoginInfo> ShowLoginDialogAsync()
        {
            try
            {
                ETLogManager.Info("显示登录对话框");

                _loginTaskCompletionSource = new TaskCompletionSource<ETOALoginInfo>();
                _autoLoginMode = false;

                this.FormClosed += (s, e) =>
                {
                    if (!_loginTaskCompletionSource.Task.IsCompleted)
                    {
                        _loginTaskCompletionSource.SetResult(_loginInfo);
                    }
                };

                this.ShowDialog();
                return await _loginTaskCompletionSource.Task;
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"显示登录对话框失败: {ex.Message}", ex);
                throw new ETOAException("显示登录对话框失败", ex);
            }
        }

        /// <summary>
        /// 自动登录
        /// </summary>
        /// <param name="username">用户名</param>
        /// <param name="password">密码</param>
        /// <returns>登录是否成功</returns>
        public async Task<bool> AutoLoginAsync(string username, string password)
        {
            try
            {
                ETLogManager.Info($"开始自动登录，用户名: {username}");

                if (string.IsNullOrEmpty(username) || string.IsNullOrEmpty(password))
                {
                    throw new ArgumentException("用户名和密码不能为空");
                }

                _username = username;
                _password = password;
                _autoLoginMode = true;
                _loginTaskCompletionSource = new TaskCompletionSource<ETOALoginInfo>();

                // 等待浏览器初始化完成
                if (!_browser.IsBrowserInitialized)
                {
                    await _browser.WaitForInitializationAsync();
                }

                // 导航到登录页面
                _browser.Load(LoginUrl);

                // 等待登录完成或超时
                var timeoutTask = Task.Delay(TimeSpan.FromMinutes(2));
                var completedTask = await Task.WhenAny(_loginTaskCompletionSource.Task, timeoutTask);

                if (completedTask == timeoutTask)
                {
                    ETLogManager.Warn("自动登录超时");
                    return false;
                }

                var result = _isLoginSuccessful;
                ETLogManager.Info($"自动登录完成，结果: {result}");
                return result;
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"自动登录失败: {ex.Message}", ex);
                return false;
            }
        }

        /// <summary>
        /// 获取登录信息
        /// </summary>
        /// <returns>登录信息</returns>
        public ETOALoginInfo GetLoginInfo()
        {
            return _loginInfo;
        }

        /// <summary>
        /// 关闭浏览器
        /// </summary>
        public new void Close()
        {
            try
            {
                ETLogManager.Info("关闭登录浏览器");

                _loginCheckTimer?.Stop();
                _loginCheckTimer?.Dispose();
                _browser?.Dispose();

                base.Close();
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"关闭浏览器失败: {ex.Message}", ex);
            }
        }

        #endregion 公共方法
    }

    /// <summary>
    /// Cookie访问器，用于获取浏览器中的所有Cookie
    /// </summary>
    public class CookieVisitor : ICookieVisitor
    {
        private readonly TaskCompletionSource<bool> _taskCompletionSource = new TaskCompletionSource<bool>();

        /// <summary>
        /// 获取到的Cookie列表
        /// </summary>
        public List<Cookie> Cookies { get; } = new List<Cookie>();

        /// <summary>
        /// 异步任务
        /// </summary>
        public Task Task => _taskCompletionSource.Task;

        /// <summary>
        /// 访问Cookie
        /// </summary>
        /// <param name="cookie">Cookie对象</param>
        /// <param name="count">当前索引</param>
        /// <param name="total">总数</param>
        /// <param name="deleteCookie">是否删除Cookie</param>
        /// <returns>是否继续访问</returns>
        public bool Visit(Cookie cookie, int count, int total, ref bool deleteCookie)
        {
            try
            {
                Cookies.Add(cookie);

                // 如果是最后一个Cookie，完成任务
                if (count == total - 1)
                {
                    _taskCompletionSource.SetResult(true);
                }

                return true;
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"访问Cookie失败: {ex.Message}", ex);
                _taskCompletionSource.SetException(ex);
                return false;
            }
        }
    }

    /// <summary>
    /// ETOAException异常类
    /// </summary>
    public class ETOAException : ETException
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="message">错误消息</param>
        public ETOAException(string message) : base(message) { }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="message">错误消息</param>
        /// <param name="innerException">内部异常</param>
        public ETOAException(string message, Exception innerException) : base(message, innerException) { }

        /// <summary>
        /// 错误代码常量
        /// </summary>
        public static class ErrorCodes
        {
            public const string AUTHENTICATION_FAILED = "OA_AUTH_001";
            public const string API_REQUEST_FAILED = "OA_API_002";
            public const string BROWSER_OPERATION_FAILED = "OA_BROWSER_003";
            public const string SESSION_EXPIRED = "OA_SESSION_004";
            public const string FILE_UPLOAD_FAILED = "OA_UPLOAD_005";
        }
    }
}