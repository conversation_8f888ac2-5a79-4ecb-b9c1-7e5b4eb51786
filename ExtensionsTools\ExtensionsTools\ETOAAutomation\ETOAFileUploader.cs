using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using System.Security.Cryptography;
using System.Text;
using System.Threading;
using Flurl.Http;
using ET.ETOAAutomation.Models;
using ET.ETOAAutomation.Helpers;
using ET;
using System.Linq;

namespace ET.ETOAAutomation
{
    /// <summary>
    /// 文件上传处理器，支持单文件和批量文件上传
    /// </summary>
    public class ETOAFileUploader
    {
        #region 私有字段

        private readonly ETOAApiClient _apiClient;
        private Action<int> _progressCallback;
        private Action<string, long, long> _detailedProgressCallback;
        private readonly Dictionary<string, UploadSession> _uploadSessions;
        private readonly SemaphoreSlim _uploadSemaphore;

        #endregion 私有字段

        #region 公共属性

        /// <summary>
        /// 最大文件大小（MB）
        /// </summary>
        public int MaxFileSize { get; set; } = 100;

        /// <summary>
        /// 允许的文件扩展名
        /// </summary>
        public string[] AllowedExtensions { get; set; } =
        {
            ".jpg", ".jpeg", ".png", ".gif", ".bmp",
            ".pdf", ".doc", ".docx", ".xls", ".xlsx",
            ".ppt", ".pptx", ".txt", ".zip", ".rar"
        };

        /// <summary>
        /// 是否启用断点续传
        /// </summary>
        public bool EnableResumableUpload { get; set; } = true;

        /// <summary>
        /// 分块上传的块大小（字节）
        /// </summary>
        public int ChunkSize { get; set; } = 1024 * 1024; // 1MB

        /// <summary>
        /// 上传重试次数
        /// </summary>
        public int RetryCount { get; set; } = 3;

        /// <summary>
        /// 最大并发上传数
        /// </summary>
        public int MaxConcurrentUploads { get; set; } = 3;

        /// <summary>
        /// 是否启用文件完整性校验
        /// </summary>
        public bool EnableIntegrityCheck { get; set; } = true;

        #endregion 公共属性

        #region 构造函数

        /// <summary>
        /// 初始化文件上传器
        /// </summary>
        /// <param name="apiClient">API客户端</param>
        public ETOAFileUploader(ETOAApiClient apiClient)
        {
            _apiClient = apiClient ?? throw new ArgumentNullException(nameof(apiClient));
            _uploadSessions = new Dictionary<string, UploadSession>();
            _uploadSemaphore = new SemaphoreSlim(MaxConcurrentUploads, MaxConcurrentUploads);

            ETLogManager.Info("ETOAFileUploader", "文件上传器初始化完成");
        }

        #endregion 构造函数

        #region 私有方法

        /// <summary>
        /// 验证文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>验证结果</returns>
        private (bool IsValid, string ErrorMessage) ValidateFile(string filePath)
        {
            if (!File.Exists(filePath))
            {
                return (false, "文件不存在");
            }

            var fileInfo = new FileInfo(filePath);

            // 检查文件大小
            if (fileInfo.Length > MaxFileSize * 1024 * 1024)
            {
                return (false, $"文件大小超过限制 ({MaxFileSize}MB)");
            }

            // 检查文件扩展名
            var extension = fileInfo.Extension.ToLower();
            if (AllowedExtensions != null && AllowedExtensions.Length > 0)
            {
                bool isAllowed = false;
                foreach (var allowedExt in AllowedExtensions)
                {
                    if (extension == allowedExt.ToLower())
                    {
                        isAllowed = true;
                        break;
                    }
                }

                if (!isAllowed)
                {
                    return (false, $"不支持的文件类型: {extension}");
                }
            }

            return (true, null);
        }

        /// <summary>
        /// 创建上传结果
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="success">是否成功</param>
        /// <param name="message">消息</param>
        /// <param name="fileId">文件ID</param>
        /// <returns>上传结果</returns>
        private ETOAUploadResult CreateUploadResult(string filePath, bool success, string message, string fileId = null)
        {
            return new ETOAUploadResult
            {
                FileName = Path.GetFileName(filePath),
                FilePath = filePath,
                IsSuccess = success,
                Message = message,
                FileId = fileId,
                UploadTime = DateTime.Now
            };
        }

        /// <summary>
        /// 计算文件MD5哈希值
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>MD5哈希值</returns>
        private async Task<string> CalculateFileMD5Async(string filePath)
        {
            try
            {
                using (var md5 = MD5.Create())
                using (var stream = File.OpenRead(filePath))
                {
                    var hash = await Task.Run(() => md5.ComputeHash(stream));
                    return BitConverter.ToString(hash).Replace("-", "").ToLowerInvariant();
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error("ETOAFileUploader", $"计算文件MD5失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 生成上传会话ID
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>会话ID</returns>
        private string GenerateUploadSessionId(string filePath)
        {
            var fileInfo = new FileInfo(filePath);
            var sessionData = $"{filePath}_{fileInfo.Length}_{fileInfo.LastWriteTime:yyyyMMddHHmmss}";
            using (var md5 = MD5.Create())
            {
                var hash = md5.ComputeHash(Encoding.UTF8.GetBytes(sessionData));
                return BitConverter.ToString(hash).Replace("-", "").ToLowerInvariant();
            }
        }

        /// <summary>
        /// 检查服务器是否支持断点续传
        /// </summary>
        /// <param name="endpoint">上传端点</param>
        /// <returns>是否支持断点续传</returns>
        private async Task<bool> CheckResumableUploadSupportAsync(string endpoint)
        {
            try
            {
                var response = await _apiClient.BaseUrl
                    .AppendPathSegment(endpoint)
                    .AllowHttpStatus("405,501")
                    .SendAsync(HttpMethod.Head);

                return response.Headers.Contains("Accept-Ranges") ||
                       response.Headers.Contains("X-Resumable-Upload");
            }
            catch
            {
                return false;
            }
        }

        #endregion 私有方法

        #region 公共方法

        /// <summary>
        /// 设置进度回调
        /// </summary>
        /// <param name="progressCallback">进度回调方法</param>
        public void SetProgressCallback(Action<int> progressCallback)
        {
            _progressCallback = progressCallback;
        }

        /// <summary>
        /// 设置详细进度回调
        /// </summary>
        /// <param name="detailedProgressCallback">详细进度回调方法（文件名，已上传字节，总字节）</param>
        public void SetDetailedProgressCallback(Action<string, long, long> detailedProgressCallback)
        {
            _detailedProgressCallback = detailedProgressCallback;
        }

        /// <summary>
        /// 获取上传会话信息
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>上传会话信息</returns>
        public UploadSession GetUploadSession(string filePath)
        {
            var sessionId = GenerateUploadSessionId(filePath);
            return _uploadSessions.ContainsKey(sessionId) ? _uploadSessions[sessionId] : null;
        }

        /// <summary>
        /// 清除上传会话
        /// </summary>
        /// <param name="filePath">文件路径</param>
        public void ClearUploadSession(string filePath)
        {
            var sessionId = GenerateUploadSessionId(filePath);
            if (_uploadSessions.ContainsKey(sessionId))
            {
                _uploadSessions.Remove(sessionId);
                ETLogManager.Info("ETOAFileUploader", $"清除上传会话: {Path.GetFileName(filePath)}");
            }
        }

        /// <summary>
        /// 获取所有活跃的上传会话
        /// </summary>
        /// <returns>活跃的上传会话列表</returns>
        public List<UploadSession> GetActiveUploadSessions()
        {
            return new List<UploadSession>(_uploadSessions.Values);
        }

        /// <summary>
        /// 暂停文件上传
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>是否成功暂停</returns>
        public bool PauseUpload(string filePath)
        {
            var session = GetUploadSession(filePath);
            if (session != null && !session.IsCompleted)
            {
                session.IsPaused = true;
                ETLogManager.Info("ETOAFileUploader", $"暂停上传: {Path.GetFileName(filePath)}");
                return true;
            }
            return false;
        }

        /// <summary>
        /// 恢复文件上传
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>是否成功恢复</returns>
        public bool ResumeUpload(string filePath)
        {
            var session = GetUploadSession(filePath);
            if (session != null && session.IsPaused)
            {
                session.IsPaused = false;
                ETLogManager.Info("ETOAFileUploader", $"恢复上传: {Path.GetFileName(filePath)}");
                return true;
            }
            return false;
        }

        /// <summary>
        /// 取消文件上传
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>是否成功取消</returns>
        public bool CancelUpload(string filePath)
        {
            var session = GetUploadSession(filePath);
            if (session != null && !session.IsCompleted)
            {
                session.IsCancelled = true;
                ClearUploadSession(filePath);
                ETLogManager.Info("ETOAFileUploader", $"取消上传: {Path.GetFileName(filePath)}");
                return true;
            }
            return false;
        }

        /// <summary>
        /// 获取上传统计信息
        /// </summary>
        /// <returns>上传统计信息</returns>
        public UploadStatistics GetUploadStatistics()
        {
            var sessions = _uploadSessions.Values.ToList();
            return new UploadStatistics
            {
                TotalSessions = sessions.Count,
                CompletedSessions = sessions.Count(s => s.IsCompleted),
                ActiveSessions = sessions.Count(s => !s.IsCompleted && !s.IsCancelled && !s.IsPaused),
                PausedSessions = sessions.Count(s => s.IsPaused),
                CancelledSessions = sessions.Count(s => s.IsCancelled),
                TotalBytes = sessions.Sum(s => s.FileSize),
                UploadedBytes = sessions.Sum(s => s.UploadedBytes),
                AverageSpeed = sessions.Where(s => s.UploadSpeed > 0).DefaultIfEmpty().Average(s => s?.UploadSpeed ?? 0)
            };
        }

        /// <summary>
        /// 清除所有上传会话
        /// </summary>
        public void ClearAllUploadSessions()
        {
            _uploadSessions.Clear();
            ETLogManager.Info("ETOAFileUploader", "清除所有上传会话");
        }

        /// <summary>
        /// 验证文件完整性
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="expectedMD5">期望的MD5值</param>
        /// <returns>是否完整</returns>
        public async Task<bool> ValidateFileIntegrityAsync(string filePath, string expectedMD5)
        {
            try
            {
                var actualMD5 = await CalculateFileMD5Async(filePath);
                return string.Equals(actualMD5, expectedMD5, StringComparison.OrdinalIgnoreCase);
            }
            catch (Exception ex)
            {
                ETLogManager.Error("ETOAFileUploader", $"文件完整性验证失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            _uploadSemaphore?.Dispose();
            _uploadSessions.Clear();
            ETLogManager.Info("ETOAFileUploader", "文件上传器资源已释放");
        }

        /// <summary>
        /// 上传单个文件
        /// </summary>
        /// <param name="endpoint">上传端点</param>
        /// <param name="filePath">文件路径</param>
        /// <param name="formData">表单数据</param>
        /// <returns>上传结果</returns>
        public async Task<ETOAUploadResult> UploadFileAsync(string endpoint, string filePath, object formData = null)
        {
            return await UploadFileWithRetryAsync(endpoint, filePath, formData, RetryCount);
        }

        /// <summary>
        /// 上传单个文件（带重试）
        /// </summary>
        /// <param name="endpoint">上传端点</param>
        /// <param name="filePath">文件路径</param>
        /// <param name="formData">表单数据</param>
        /// <param name="retryCount">重试次数</param>
        /// <returns>上传结果</returns>
        public async Task<ETOAUploadResult> UploadFileWithRetryAsync(string endpoint, string filePath, object formData = null, int retryCount = 3)
        {
            ETOAUploadResult lastResult = null;

            for (int attempt = 0; attempt <= retryCount; attempt++)
            {
                try
                {
                    // 验证文件
                    var validation = ValidateFile(filePath);
                    if (!validation.IsValid)
                    {
                        return CreateUploadResult(filePath, false, validation.ErrorMessage);
                    }

                    var fileName = Path.GetFileName(filePath);
                    var fileInfo = new FileInfo(filePath);

                    ETLogManager.Info("ETOAFileUploader",
                        $"开始上传文件: {fileName} (尝试 {attempt + 1}/{retryCount + 1})");

                    // 检查是否启用断点续传
                    if (EnableResumableUpload && fileInfo.Length > ChunkSize)
                    {
                        lastResult = await UploadFileResumableAsync(endpoint, filePath, formData);
                    }
                    else
                    {
                        lastResult = await UploadFileDirectAsync(endpoint, filePath, formData);
                    }

                    if (lastResult.IsSuccess)
                    {
                        ETLogManager.Info("ETOAFileUploader", $"文件上传成功: {fileName}");
                        return lastResult;
                    }

                    if (attempt < retryCount)
                    {
                        var delay = (int)Math.Pow(2, attempt) * 1000; // 指数退避
                        ETLogManager.Instance.LogWarning("ETOAFileUploader",
                            $"上传失败，{delay}ms后重试: {lastResult.Message}");
                        await Task.Delay(delay);
                    }
                }
                catch (Exception ex)
                {
                    var errorMsg = $"上传异常 (尝试 {attempt + 1}/{retryCount + 1}): {ex.Message}";
                    ETLogManager.Error("ETOAFileUploader", errorMsg);

                    lastResult = CreateUploadResult(filePath, false, errorMsg);

                    if (attempt < retryCount)
                    {
                        var delay = (int)Math.Pow(2, attempt) * 1000;
                        await Task.Delay(delay);
                    }
                }
            }

            return lastResult ?? CreateUploadResult(filePath, false, "上传失败：未知错误");
        }

        /// <summary>
        /// 批量上传文件
        /// </summary>
        /// <param name="endpoint">上传端点</param>
        /// <param name="filePaths">文件路径数组</param>
        /// <param name="formData">表单数据</param>
        /// <returns>上传结果列表</returns>
        public async Task<List<ETOAUploadResult>> UploadFilesAsync(string endpoint, string[] filePaths, object formData = null)
        {
            return await UploadFilesConcurrentAsync(endpoint, filePaths, formData, MaxConcurrentUploads);
        }

        /// <summary>
        /// 并发批量上传文件
        /// </summary>
        /// <param name="endpoint">上传端点</param>
        /// <param name="filePaths">文件路径数组</param>
        /// <param name="formData">表单数据</param>
        /// <param name="maxConcurrency">最大并发数</param>
        /// <returns>上传结果列表</returns>
        public async Task<List<ETOAUploadResult>> UploadFilesConcurrentAsync(string endpoint, string[] filePaths, object formData = null, int maxConcurrency = 3)
        {
            var results = new List<ETOAUploadResult>();

            if (filePaths == null || filePaths.Length == 0)
            {
                return results;
            }

            ETLogManager.Info("ETOAFileUploader", $"开始批量上传 {filePaths.Length} 个文件，最大并发数: {maxConcurrency}");

            var semaphore = new SemaphoreSlim(maxConcurrency, maxConcurrency);
            var tasks = new List<Task<ETOAUploadResult>>();
            var completedCount = 0;

            foreach (var filePath in filePaths)
            {
                var task = UploadFileWithSemaphoreAsync(endpoint, filePath, formData, semaphore, () =>
                {
                    Interlocked.Increment(ref completedCount);
                    var progress = (int)((completedCount * 100.0) / filePaths.Length);
                    _progressCallback?.Invoke(progress);
                });

                tasks.Add(task);
            }

            var uploadResults = await Task.WhenAll(tasks);
            results.AddRange(uploadResults);

            var successCount = results.Count(r => r.IsSuccess);
            ETLogManager.Info("ETOAFileUploader",
                $"批量上传完成: 成功 {successCount}/{filePaths.Length} 个文件");

            return results;
        }

        /// <summary>
        /// 使用信号量控制并发的文件上传
        /// </summary>
        /// <param name="endpoint">上传端点</param>
        /// <param name="filePath">文件路径</param>
        /// <param name="formData">表单数据</param>
        /// <param name="semaphore">信号量</param>
        /// <param name="onCompleted">完成回调</param>
        /// <returns>上传结果</returns>
        private async Task<ETOAUploadResult> UploadFileWithSemaphoreAsync(string endpoint, string filePath, object formData, SemaphoreSlim semaphore, Action onCompleted)
        {
            await semaphore.WaitAsync();
            try
            {
                var result = await UploadFileAsync(endpoint, filePath, formData);
                onCompleted?.Invoke();
                return result;
            }
            finally
            {
                semaphore.Release();
            }
        }

        /// <summary>
        /// 直接上传文件（不分块）
        /// </summary>
        /// <param name="endpoint">上传端点</param>
        /// <param name="filePath">文件路径</param>
        /// <param name="formData">表单数据</param>
        /// <returns>上传结果</returns>
        private async Task<ETOAUploadResult> UploadFileDirectAsync(string endpoint, string filePath, object formData = null)
        {
            try
            {
                var fileName = Path.GetFileName(filePath);
                var fileBytes = await File.ReadAllBytesAsync(filePath);

                // 计算文件MD5（如果启用完整性校验）
                string fileMD5 = null;
                if (EnableIntegrityCheck)
                {
                    fileMD5 = await CalculateFileMD5Async(filePath);
                }

                // 创建多部分表单数据
                var request = _apiClient.BaseUrl
                    .AppendPathSegment(endpoint)
                    .PostMultipartAsync(mp =>
                    {
                        mp.AddFile("file", fileBytes, fileName);

                        // 添加MD5校验值
                        if (!string.IsNullOrEmpty(fileMD5))
                        {
                            mp.AddString("fileMD5", fileMD5);
                        }

                        // 添加表单数据
                        if (formData != null)
                        {
                            var properties = formData.GetType().GetProperties();
                            foreach (var prop in properties)
                            {
                                var value = prop.GetValue(formData);
                                if (value != null)
                                {
                                    mp.AddString(prop.Name, value.ToString());
                                }
                            }
                        }
                    });

                var response = await request;
                var result = await response.GetJsonAsync<dynamic>();

                // 报告进度
                _progressCallback?.Invoke(100);
                _detailedProgressCallback?.Invoke(fileName, fileBytes.Length, fileBytes.Length);

                return CreateUploadResult(filePath, true, "上传成功", result?.fileId?.ToString());
            }
            catch (Exception ex)
            {
                return CreateUploadResult(filePath, false, $"直接上传失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 断点续传上传文件
        /// </summary>
        /// <param name="endpoint">上传端点</param>
        /// <param name="filePath">文件路径</param>
        /// <param name="formData">表单数据</param>
        /// <returns>上传结果</returns>
        private async Task<ETOAUploadResult> UploadFileResumableAsync(string endpoint, string filePath, object formData = null)
        {
            try
            {
                var fileName = Path.GetFileName(filePath);
                var fileInfo = new FileInfo(filePath);
                var sessionId = GenerateUploadSessionId(filePath);

                // 获取或创建上传会话
                if (!_uploadSessions.ContainsKey(sessionId))
                {
                    _uploadSessions[sessionId] = new UploadSession
                    {
                        SessionId = sessionId,
                        FilePath = filePath,
                        FileName = fileName,
                        FileSize = fileInfo.Length,
                        UploadedBytes = 0,
                        StartTime = DateTime.Now,
                        LastUpdateTime = DateTime.Now
                    };
                }

                var session = _uploadSessions[sessionId];

                // 检查服务器是否支持断点续传
                var supportsResumable = await CheckResumableUploadSupportAsync(endpoint);
                if (!supportsResumable)
                {
                    ETLogManager.Instance.LogWarning("ETOAFileUploader", "服务器不支持断点续传，使用直接上传");
                    return await UploadFileDirectAsync(endpoint, filePath, formData);
                }

                // 分块上传
                using (var fileStream = File.OpenRead(filePath))
                {
                    var buffer = new byte[ChunkSize];
                    var totalChunks = (int)Math.Ceiling((double)fileInfo.Length / ChunkSize);
                    var currentChunk = (int)(session.UploadedBytes / ChunkSize);

                    fileStream.Seek(session.UploadedBytes, SeekOrigin.Begin);

                    while (currentChunk < totalChunks)
                    {
                        var bytesRead = await fileStream.ReadAsync(buffer, 0, ChunkSize);
                        if (bytesRead == 0) break;

                        var chunkData = new byte[bytesRead];
                        Array.Copy(buffer, chunkData, bytesRead);

                        // 上传分块
                        var chunkResult = await UploadChunkAsync(endpoint, chunkData, currentChunk, totalChunks, session, formData);
                        if (!chunkResult.IsSuccess)
                        {
                            return CreateUploadResult(filePath, false, $"分块上传失败: {chunkResult.Message}");
                        }

                        // 更新进度
                        session.UploadedBytes += bytesRead;
                        session.LastUpdateTime = DateTime.Now;

                        var progress = (int)((session.UploadedBytes * 100) / session.FileSize);
                        _progressCallback?.Invoke(progress);
                        _detailedProgressCallback?.Invoke(fileName, session.UploadedBytes, session.FileSize);

                        currentChunk++;
                    }
                }

                // 完成上传
                session.IsCompleted = true;
                session.CompletedTime = DateTime.Now;

                return CreateUploadResult(filePath, true, "断点续传上传成功", session.FileId);
            }
            catch (Exception ex)
            {
                return CreateUploadResult(filePath, false, $"断点续传上传失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 上传文件分块
        /// </summary>
        /// <param name="endpoint">上传端点</param>
        /// <param name="chunkData">分块数据</param>
        /// <param name="chunkIndex">分块索引</param>
        /// <param name="totalChunks">总分块数</param>
        /// <param name="session">上传会话</param>
        /// <param name="formData">表单数据</param>
        /// <returns>上传结果</returns>
        private async Task<ETOAUploadResult> UploadChunkAsync(string endpoint, byte[] chunkData, int chunkIndex, int totalChunks, UploadSession session, object formData = null)
        {
            try
            {
                var request = _apiClient.BaseUrl
                    .AppendPathSegment(endpoint)
                    .PostMultipartAsync(mp =>
                    {
                        mp.AddFile("chunk", chunkData, $"{session.FileName}.part{chunkIndex}");
                        mp.AddString("sessionId", session.SessionId);
                        mp.AddString("chunkIndex", chunkIndex.ToString());
                        mp.AddString("totalChunks", totalChunks.ToString());
                        mp.AddString("fileName", session.FileName);
                        mp.AddString("fileSize", session.FileSize.ToString());

                        // 添加表单数据（仅在第一个分块时）
                        if (chunkIndex == 0 && formData != null)
                        {
                            var properties = formData.GetType().GetProperties();
                            foreach (var prop in properties)
                            {
                                var value = prop.GetValue(formData);
                                if (value != null)
                                {
                                    mp.AddString(prop.Name, value.ToString());
                                }
                            }
                        }
                    });

                var response = await request;
                var result = await response.GetJsonAsync<dynamic>();

                // 如果是最后一个分块，保存文件ID
                if (chunkIndex == totalChunks - 1)
                {
                    session.FileId = result?.fileId?.ToString();
                }

                return CreateUploadResult(session.FilePath, true, "分块上传成功");
            }
            catch (Exception ex)
            {
                return CreateUploadResult(session.FilePath, false, $"分块上传失败: {ex.Message}");
            }
        }

        #endregion 公共方法

        #region 内部类

        /// <summary>
        /// 上传会话信息
        /// </summary>
        public class UploadSession
        {
            /// <summary>
            /// 会话ID
            /// </summary>
            public string SessionId { get; set; }

            /// <summary>
            /// 文件路径
            /// </summary>
            public string FilePath { get; set; }

            /// <summary>
            /// 文件名
            /// </summary>
            public string FileName { get; set; }

            /// <summary>
            /// 文件大小
            /// </summary>
            public long FileSize { get; set; }

            /// <summary>
            /// 已上传字节数
            /// </summary>
            public long UploadedBytes { get; set; }

            /// <summary>
            /// 开始时间
            /// </summary>
            public DateTime StartTime { get; set; }

            /// <summary>
            /// 最后更新时间
            /// </summary>
            public DateTime LastUpdateTime { get; set; }

            /// <summary>
            /// 完成时间
            /// </summary>
            public DateTime? CompletedTime { get; set; }

            /// <summary>
            /// 是否已完成
            /// </summary>
            public bool IsCompleted { get; set; }

            /// <summary>
            /// 是否已暂停
            /// </summary>
            public bool IsPaused { get; set; }

            /// <summary>
            /// 是否已取消
            /// </summary>
            public bool IsCancelled { get; set; }

            /// <summary>
            /// 文件ID（上传成功后的服务器文件ID）
            /// </summary>
            public string FileId { get; set; }

            /// <summary>
            /// 上传进度百分比
            /// </summary>
            public int ProgressPercentage => FileSize > 0 ? (int)((UploadedBytes * 100) / FileSize) : 0;

            /// <summary>
            /// 上传速度（字节/秒）
            /// </summary>
            public long UploadSpeed
            {
                get
                {
                    var elapsed = (LastUpdateTime - StartTime).TotalSeconds;
                    return elapsed > 0 ? (long)(UploadedBytes / elapsed) : 0;
                }
            }
        }

        /// <summary>
        /// 上传统计信息
        /// </summary>
        public class UploadStatistics
        {
            /// <summary>
            /// 总会话数
            /// </summary>
            public int TotalSessions { get; set; }

            /// <summary>
            /// 已完成会话数
            /// </summary>
            public int CompletedSessions { get; set; }

            /// <summary>
            /// 活跃会话数
            /// </summary>
            public int ActiveSessions { get; set; }

            /// <summary>
            /// 暂停会话数
            /// </summary>
            public int PausedSessions { get; set; }

            /// <summary>
            /// 取消会话数
            /// </summary>
            public int CancelledSessions { get; set; }

            /// <summary>
            /// 总字节数
            /// </summary>
            public long TotalBytes { get; set; }

            /// <summary>
            /// 已上传字节数
            /// </summary>
            public long UploadedBytes { get; set; }

            /// <summary>
            /// 平均上传速度（字节/秒）
            /// </summary>
            public double AverageSpeed { get; set; }

            /// <summary>
            /// 总体进度百分比
            /// </summary>
            public int OverallProgress => TotalBytes > 0 ? (int)((UploadedBytes * 100) / TotalBytes) : 0;

            /// <summary>
            /// 成功率
            /// </summary>
            public double SuccessRate => TotalSessions > 0 ? (double)CompletedSessions / TotalSessions * 100 : 0;
        }

        #endregion 内部类
    }
}